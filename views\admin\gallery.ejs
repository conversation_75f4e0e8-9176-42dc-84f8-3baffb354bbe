<% layout('layout') -%>

<div class="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
  <div>
    <h2 class="text-2xl font-bold">Gallery Management</h2>
    <p class="text-gray-400 text-sm mt-1">Manage all user videos across the platform</p>
  </div>
  <div class="flex flex-col sm:flex-row gap-3">
    <button onclick="refreshGallery()" 
      class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
      <i class="ti ti-refresh"></i>
      <span>Refresh</span>
    </button>
  </div>
</div>

<!-- Gallery Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6">
  <!-- Total Videos Card -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Total Videos</h3>
      <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
        <i class="ti ti-video text-xl text-white"></i>
      </div>
    </div>
    <p class="text-3xl font-bold mt-2">
      <span class="gradient-text"><%= totalVideos %></span>
    </p>
    <p class="text-sm text-gray-400 mt-2">
      <i class="ti ti-info-circle mr-1"></i>
      Videos across all users
    </p>
  </div>

  <!-- Active Users Card -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Users with Videos</h3>
      <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
        <i class="ti ti-users text-xl text-white"></i>
      </div>
    </div>
    <p class="text-3xl font-bold mt-2">
      <span class="gradient-text"><%= users.filter(u => videos.some(v => v.user_id === u.id)).length %></span>
    </p>
    <p class="text-sm text-gray-400 mt-2">
      <i class="ti ti-info-circle mr-1"></i>
      Users who have uploaded videos
    </p>
  </div>

  <!-- Storage Usage Card -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Total Storage</h3>
      <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
        <i class="ti ti-database text-xl text-white"></i>
      </div>
    </div>
    <p class="text-3xl font-bold mt-2">
      <span class="gradient-text">
        <%= (videos.reduce((total, video) => total + (video.file_size || 0), 0) / (1024 * 1024 * 1024)).toFixed(2) %>GB
      </span>
    </p>
    <p class="text-sm text-gray-400 mt-2">
      <i class="ti ti-info-circle mr-1"></i>
      Total video storage used
    </p>
  </div>
</div>

<!-- Search and Filter Controls -->
<div class="bg-gray-800 rounded-lg p-4 mb-6">
  <form method="GET" action="/admin/gallery" class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
    <!-- Search Input -->
    <div class="relative flex-1 sm:max-w-80 md:max-w-96">
      <input type="text" name="search" value="<%= search %>" placeholder="Search videos, users, or emails..."
        class="w-full bg-dark-700 text-white pl-9 pr-4 py-2.5 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
      <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
    </div>

    <!-- User Filter -->
    <div class="relative">
      <select name="userId" class="bg-dark-700 text-white px-4 py-2.5 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary min-w-48">
        <option value="">All Users</option>
        <% users.forEach(user => { %>
          <option value="<%= user.id %>" <%= selectedUserId === user.id ? 'selected' : '' %>>
            <%= user.username %> (<%= user.plan_type || 'No Plan' %>)
          </option>
        <% }); %>
      </select>
    </div>

    <!-- Search Button -->
    <button type="submit" class="bg-primary hover:bg-secondary text-white px-6 py-2.5 rounded-lg transition-colors">
      <i class="ti ti-search mr-2"></i>
      Search
    </button>

    <!-- Clear Filters -->
    <% if (search || selectedUserId) { %>
      <a href="/admin/gallery" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2.5 rounded-lg transition-colors text-center">
        <i class="ti ti-x mr-2"></i>
        Clear
      </a>
    <% } %>
  </form>
</div>

<!-- Videos Grid -->
<div class="bg-gray-800 rounded-lg p-6">
  <% if (videos && videos.length > 0) { %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <% videos.forEach(video => { %>
        <div class="bg-gray-700 rounded-lg overflow-hidden hover:bg-gray-600 transition-colors group">
          <!-- Video Thumbnail -->
          <div class="relative aspect-video bg-gray-900">
            <% if (video.thumbnail_path) { %>
              <img src="<%= video.thumbnail_path %>" alt="<%= video.title %>" 
                class="w-full h-full object-cover">
            <% } else { %>
              <div class="w-full h-full flex items-center justify-center">
                <i class="ti ti-video text-4xl text-gray-500"></i>
              </div>
            <% } %>
            
            <!-- Video Duration -->
            <% if (video.duration) { %>
              <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                <%= Math.floor(video.duration / 60) %>:<%= String(Math.floor(video.duration % 60)).padStart(2, '0') %>
              </div>
            <% } %>

            <!-- Processing Status -->
            <% if (video.processing_status && video.processing_status !== 'completed') { %>
              <div class="absolute top-2 left-2 bg-yellow-600 text-white text-xs px-2 py-1 rounded">
                <%= video.processing_status.charAt(0).toUpperCase() + video.processing_status.slice(1) %>
              </div>
            <% } %>
          </div>

          <!-- Video Info -->
          <div class="p-4">
            <h3 class="font-semibold text-white truncate mb-2" title="<%= video.title %>">
              <%= video.title %>
            </h3>
            
            <!-- User Info -->
            <div class="flex items-center gap-2 mb-2">
              <i class="ti ti-user text-gray-400 text-sm"></i>
              <span class="text-sm text-gray-300">
                <%= video.username || 'Unknown User' %>
              </span>
              <% if (video.role === 'admin') { %>
                <span class="bg-red-600 text-white text-xs px-2 py-0.5 rounded">Admin</span>
              <% } %>
            </div>

            <!-- Plan Info -->
            <div class="flex items-center gap-2 mb-2">
              <i class="ti ti-crown text-gray-400 text-sm"></i>
              <span class="text-sm text-gray-300">
                <%= video.plan_type || 'No Plan' %>
              </span>
            </div>

            <!-- File Info -->
            <div class="flex items-center justify-between text-xs text-gray-400 mb-3">
              <span>
                <%= video.format ? video.format.toUpperCase() : 'Unknown' %>
                <% if (video.resolution) { %>
                  • <%= video.resolution %>
                <% } %>
              </span>
              <span>
                <%= video.file_size ? (video.file_size / (1024 * 1024)).toFixed(1) + 'MB' : 'Unknown size' %>
              </span>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2">
              <button onclick="viewUserGallery('<%= video.user_id %>', '<%= video.username %>')"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 rounded transition-colors">
                <i class="ti ti-eye mr-1"></i>
                View User Gallery
              </button>
              <button onclick="deleteVideo('<%= video.id %>', '<%= video.title %>', '<%= video.username %>')"
                class="bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-2 rounded transition-colors">
                <i class="ti ti-trash"></i>
              </button>
            </div>
          </div>
        </div>
      <% }); %>
    </div>

    <!-- Pagination -->
    <% if (totalPages > 1) { %>
      <div class="flex justify-center items-center gap-2 mt-8">
        <% if (hasPrevPage) { %>
          <a href="?page=<%= prevPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %><%= selectedUserId ? '&userId=' + selectedUserId : '' %>" 
            class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded transition-colors">
            <i class="ti ti-chevron-left"></i>
          </a>
        <% } %>

        <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
          <% if (i === currentPage) { %>
            <span class="bg-primary text-white px-3 py-2 rounded">
              <%= i %>
            </span>
          <% } else { %>
            <a href="?page=<%= i %><%= search ? '&search=' + encodeURIComponent(search) : '' %><%= selectedUserId ? '&userId=' + selectedUserId : '' %>" 
              class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded transition-colors">
              <%= i %>
            </a>
          <% } %>
        <% } %>

        <% if (hasNextPage) { %>
          <a href="?page=<%= nextPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %><%= selectedUserId ? '&userId=' + selectedUserId : '' %>" 
            class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded transition-colors">
            <i class="ti ti-chevron-right"></i>
          </a>
        <% } %>
      </div>
    <% } %>
  <% } else { %>
    <div class="text-center py-12">
      <i class="ti ti-video-off text-6xl text-gray-500 mb-4"></i>
      <h3 class="text-xl font-semibold text-gray-300 mb-2">No Videos Found</h3>
      <p class="text-gray-400">
        <% if (search || selectedUserId) { %>
          No videos match your current filters. Try adjusting your search criteria.
        <% } else { %>
          No videos have been uploaded to the platform yet.
        <% } %>
      </p>
    </div>
  <% } %>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
  <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
    <h3 class="text-xl font-bold text-white mb-4">Confirm Video Deletion</h3>
    <p class="text-gray-300 mb-4">
      Are you sure you want to delete the video "<span id="deleteVideoTitle" class="font-semibold"></span>" 
      by user "<span id="deleteVideoUser" class="font-semibold"></span>"?
    </p>
    <p class="text-red-400 text-sm mb-6">
      <i class="ti ti-alert-triangle mr-1"></i>
      This action cannot be undone and will permanently delete the video file.
    </p>
    <div class="flex gap-3">
      <button id="cancelDeleteBtn" onclick="closeDeleteModal()"
        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors">
        Cancel
      </button>
      <button id="confirmDeleteBtn" onclick="confirmDelete()"
        class="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center justify-center gap-2">
        <span id="deleteButtonText">Delete Video</span>
        <div id="deleteButtonSpinner" class="hidden">
          <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </button>
    </div>
  </div>
</div>

<script>
let deleteVideoId = null;
let isDeletionInProgress = false;

function refreshGallery() {
  window.location.reload();
}

function viewUserGallery(userId, username) {
  window.location.href = `/admin/gallery?userId=${userId}`;
}

function deleteVideo(videoId, videoTitle, username) {
  deleteVideoId = videoId;
  document.getElementById('deleteVideoTitle').textContent = videoTitle;
  document.getElementById('deleteVideoUser').textContent = username;

  // Reset button state when opening modal
  resetDeleteButtonState();

  document.getElementById('deleteModal').classList.remove('hidden');
  document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
  // Prevent closing modal during deletion
  if (isDeletionInProgress) {
    return;
  }

  // Reset button state when closing modal
  resetDeleteButtonState();
  deleteVideoId = null;
  isDeletionInProgress = false;
  document.getElementById('deleteModal').classList.add('hidden');
  document.getElementById('deleteModal').classList.remove('flex');
}

function setDeleteButtonLoading(isLoading) {
  const confirmBtn = document.getElementById('confirmDeleteBtn');
  const cancelBtn = document.getElementById('cancelDeleteBtn');
  const buttonText = document.getElementById('deleteButtonText');
  const spinner = document.getElementById('deleteButtonSpinner');

  if (isLoading) {
    // Show loading state
    confirmBtn.disabled = true;
    cancelBtn.disabled = true;
    confirmBtn.classList.add('opacity-75', 'cursor-not-allowed');
    confirmBtn.classList.remove('hover:bg-red-700');
    cancelBtn.classList.add('opacity-50', 'cursor-not-allowed');
    cancelBtn.classList.remove('hover:bg-gray-700');

    buttonText.textContent = 'Deleting...';
    spinner.classList.remove('hidden');
  } else {
    // Reset to normal state
    confirmBtn.disabled = false;
    cancelBtn.disabled = false;
    confirmBtn.classList.remove('opacity-75', 'cursor-not-allowed');
    confirmBtn.classList.add('hover:bg-red-700');
    cancelBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    cancelBtn.classList.add('hover:bg-gray-700');

    buttonText.textContent = 'Delete Video';
    spinner.classList.add('hidden');
  }
}

function resetDeleteButtonState() {
  setDeleteButtonLoading(false);
}

async function confirmDelete() {
  if (!deleteVideoId || isDeletionInProgress) return;

  // Set deletion in progress flag
  isDeletionInProgress = true;

  // Set loading state immediately
  setDeleteButtonLoading(true);

  try {
    const response = await fetch(`/admin/api/videos/${deleteVideoId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.success) {
      // Show success message
      showNotification(`Video "${result.video?.title || 'Unknown'}" deleted successfully`, 'success');

      // Reset deletion flag and close modal
      isDeletionInProgress = false;
      closeDeleteModal();

      // Refresh the page after a short delay to show the notification
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      // Reset deletion flag and button state on error
      isDeletionInProgress = false;
      setDeleteButtonLoading(false);
      showNotification(result.error || 'Failed to delete video', 'error');
    }
  } catch (error) {
    console.error('Delete video error:', error);
    // Reset deletion flag and button state on error
    isDeletionInProgress = false;
    setDeleteButtonLoading(false);

    // Show more specific error message
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      showNotification('Network error: Unable to connect to server', 'error');
    } else {
      showNotification('Failed to delete video: ' + (error.message || 'Unknown error'), 'error');
    }
  }
}

function showNotification(message, type, duration = 4000) {
  // Remove any existing notifications first
  const existingNotifications = document.querySelectorAll('.admin-notification');
  existingNotifications.forEach(notif => notif.remove());

  // Create notification element
  const notification = document.createElement('div');
  notification.className = `admin-notification fixed top-4 right-4 z-[60] p-4 rounded-lg text-white max-w-sm shadow-lg transform transition-all duration-300 ${
    type === 'success' ? 'bg-green-600 border-l-4 border-green-400' : 'bg-red-600 border-l-4 border-red-400'
  }`;

  // Add slide-in animation
  notification.style.transform = 'translateX(100%)';
  notification.style.opacity = '0';

  notification.innerHTML = `
    <div class="flex items-start gap-3">
      <div class="flex-shrink-0 mt-0.5">
        <i class="ti ti-${type === 'success' ? 'check-circle' : 'alert-circle'} text-xl"></i>
      </div>
      <div class="flex-1">
        <div class="font-medium text-sm">
          ${type === 'success' ? 'Success' : 'Error'}
        </div>
        <div class="text-sm opacity-90 mt-1">
          ${message}
        </div>
      </div>
      <button onclick="this.parentElement.parentElement.remove()" class="flex-shrink-0 ml-2 text-white hover:text-gray-200 transition-colors">
        <i class="ti ti-x text-lg"></i>
      </button>
    </div>
  `;

  document.body.appendChild(notification);

  // Trigger slide-in animation
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
    notification.style.opacity = '1';
  }, 10);

  // Auto-remove notification after specified duration
  const autoRemoveTimer = setTimeout(() => {
    if (notification.parentElement) {
      // Slide out animation
      notification.style.transform = 'translateX(100%)';
      notification.style.opacity = '0';

      // Remove from DOM after animation
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }
  }, duration);

  // Store timer reference for manual removal
  notification.autoRemoveTimer = autoRemoveTimer;
}

// Add keyboard event handling for modal
document.addEventListener('keydown', function(event) {
  // Handle Escape key for modal
  if (event.key === 'Escape') {
    const modal = document.getElementById('deleteModal');
    if (modal && !modal.classList.contains('hidden')) {
      // Only close if deletion is not in progress
      if (!isDeletionInProgress) {
        closeDeleteModal();
      }
      event.preventDefault();
    }
  }
});

// Add click outside modal to close (only if not deleting)
document.getElementById('deleteModal').addEventListener('click', function(event) {
  if (event.target === this && !isDeletionInProgress) {
    closeDeleteModal();
  }
});
</script>
